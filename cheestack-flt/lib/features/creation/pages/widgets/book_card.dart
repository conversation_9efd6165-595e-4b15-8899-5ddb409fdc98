import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/shared/widgets/index.dart';
import 'package:cheestack_flt/models/index.dart';
import '../../controllers/creation_controller.dart';

/// 获取隐私设置信息
Map<String, dynamic> _getPrivacyInfo(String? privacy) {
  switch (privacy) {
    case 'free':
      return {
        'label': '公开',
        'icon': Icons.public,
        'isPublic': true,
      };
    case 'private':
      return {
        'label': '私有',
        'icon': Icons.lock,
        'isPublic': false,
      };
    case 'paid':
      return {
        'label': '收费',
        'icon': Icons.monetization_on,
        'isPublic': false,
      };
    case 'member_free':
      return {
        'label': '会员',
        'icon': Icons.star,
        'isPublic': false,
      };
    default:
      return {
        'label': '未知',
        'icon': Icons.help_outline,
        'isPublic': false,
      };
  }
}

/// 书籍卡片组件 - 列表视图
class BookListCard extends StatelessWidget {
  final BookModel book;
  final CreationController controller;
  final VoidCallback? onTap;

  const BookListCard({
    super.key,
    required this.book,
    required this.controller,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.only(bottom: AppTheme.spacingSmall),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap ?? () => controller.viewBookDetail(book),
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: AppTheme.paddingMedium,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCover(context),
              SizedBox(width: 12.w),
              Expanded(child: _buildContent(context)),
              _buildActions(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCover(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        width: 60.w,
        height: 80.h,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: book.cover?.isNotEmpty == true
            ? Simage(
                url: book.cover!,
                fit: BoxFit.cover,
                width: 60.w,
                height: 80.h,
              )
            : Icon(
                Icons.book,
                size: 32.sp,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: OxText(
                book.name ?? '未命名书籍',
                fontSize: AppTheme.fontTitle,
                fontWeight: FontWeight.bold,
                maxLines: 2,
              ),
            ),
            _buildPrivacyBadge(context),
          ],
        ),
        SizedBox(height: 4.h),
        if (book.brief?.isNotEmpty == true) ...[
          OxText(
            book.brief!,
            fontSize: AppTheme.fontSmall,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            maxLines: 2,
          ),
          SizedBox(height: 8.h),
        ],
        _buildMetaInfo(context),
      ],
    );
  }

  Widget _buildPrivacyBadge(BuildContext context) {
    final privacyInfo = _getPrivacyInfo(book.privacy);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: privacyInfo['isPublic']
            ? Theme.of(context).colorScheme.primaryContainer
            : Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            privacyInfo['icon'],
            size: 12.sp,
            color: privacyInfo['isPublic']
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: 2.w),
          OxText(
            privacyInfo['label'],
            fontSize: 10.sp,
            color: privacyInfo['isPublic']
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ],
      ),
    );
  }



  Widget _buildMetaInfo(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 14.sp,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        SizedBox(width: 4.w),
        OxText(
          _formatTime(book.createdAt),
          fontSize: AppTheme.fontSmall,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        if (book.updatedAt != book.createdAt) ...[
          SizedBox(width: 12.w),
          Icon(
            Icons.edit,
            size: 14.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: 4.w),
          OxText(
            _formatTime(book.updatedAt),
            fontSize: AppTheme.fontSmall,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ],
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
        size: 20.sp,
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(
                Icons.edit,
                size: 16.sp,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              SizedBox(width: 8.w),
              const OxText('编辑'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'cards',
          child: Row(
            children: [
              Icon(
                Icons.credit_card,
                size: 16.sp,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              SizedBox(width: 8.w),
              const OxText('管理卡片'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'duplicate',
          child: Row(
            children: [
              Icon(
                Icons.copy,
                size: 16.sp,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              SizedBox(width: 8.w),
              const OxText('复制'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(
                Icons.delete,
                size: 16.sp,
                color: Theme.of(context).colorScheme.error,
              ),
              SizedBox(width: 8.w),
              OxText(
                '删除',
                color: Theme.of(context).colorScheme.error,
              ),
            ],
          ),
        ),
      ],
      onSelected: (value) {
        switch (value) {
          case 'edit':
            controller.editBook(book);
            break;
          case 'cards':
            controller.manageBookCards(book);
            break;
          case 'duplicate':
            controller.duplicateBook(book);
            break;
          case 'delete':
            controller.deleteBook(book);
            break;
        }
      },
    );
  }

  String _formatTime(String? timeStr) {
    if (timeStr == null) return '未知';
    try {
      final time = DateTime.parse(timeStr);
      final now = DateTime.now();
      final diff = now.difference(time);

      if (diff.inDays > 0) {
        return '${diff.inDays}天前';
      } else if (diff.inHours > 0) {
        return '${diff.inHours}小时前';
      } else if (diff.inMinutes > 0) {
        return '${diff.inMinutes}分钟前';
      } else {
        return '刚刚';
      }
    } catch (e) {
      return '未知';
    }
  }
}

/// 书籍卡片组件 - 网格视图
class BookGridCard extends StatelessWidget {
  final BookModel book;
  final CreationController controller;
  final VoidCallback? onTap;

  const BookGridCard({
    super.key,
    required this.book,
    required this.controller,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      shadowColor: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: InkWell(
        onTap: onTap ?? () => controller.viewBookDetail(book),
        borderRadius: BorderRadius.circular(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCover(context),
            Expanded(
              child: _buildContent(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCover(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 140.h, // 增加封面高度
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
            child: book.cover?.isNotEmpty == true
                ? Simage(
                    url: book.cover!,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: 140.h,
                  )
                : Container(
                    width: double.infinity,
                    height: 140.h,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Theme.of(context).colorScheme.primaryContainer,
                          Theme.of(context).colorScheme.secondaryContainer,
                        ],
                      ),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.auto_stories,
                        size: 56.sp,
                        color: Theme.of(context)
                            .colorScheme
                            .onPrimaryContainer
                            .withValues(alpha: 0.7),
                      ),
                    ),
                  ),
          ),
          // 渐变遮罩，让文字更清晰
          Container(
            width: double.infinity,
            height: 140.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.1),
                ],
              ),
            ),
          ),
          Positioned(
            top: 10.h,
            right: 10.w,
            child: _buildPrivacyBadge(context),
          ),
          Positioned(
            top: 10.h,
            left: 10.w,
            child: _buildActions(context),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(12.w), // 增加内边距，让内容更舒适
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题区域
          OxText(
            book.name ?? '未命名书籍',
            fontSize: AppTheme.fontBody,
            fontWeight: FontWeight.bold,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            height: 1.3, // 行高
          ),
          SizedBox(height: 6.h),
          // 简介区域
          if (book.brief?.isNotEmpty == true) ...[
            Expanded(
              child: OxText(
                book.brief!,
                fontSize: AppTheme.fontSmall,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                height: 1.4,
              ),
            ),
            SizedBox(height: 8.h),
          ] else ...[
            const Expanded(child: SizedBox()),
          ],
          // 底部元信息
          _buildMetaInfo(context),
        ],
      ),
    );
  }

  Widget _buildPrivacyBadge(BuildContext context) {
    final privacyInfo = _getPrivacyInfo(book.privacy);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: privacyInfo['isPublic']
            ? Theme.of(context)
                .colorScheme
                .primaryContainer
                .withValues(alpha: 0.9)
            : Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: privacyInfo['isPublic']
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Icon(
        privacyInfo['icon'],
        size: 14.sp,
        color: privacyInfo['isPublic']
            ? Theme.of(context).colorScheme.onPrimaryContainer
            : Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }



  Widget _buildActions(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: PopupMenuButton<String>(
        icon: Icon(
          Icons.more_vert,
          color: Theme.of(context).colorScheme.onSurface,
          size: 16.sp,
        ),
        itemBuilder: (context) => [
          PopupMenuItem(
            value: 'edit',
            child: Row(
              children: [
                Icon(
                  Icons.edit,
                  size: 16.sp,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                SizedBox(width: 8.w),
                const OxText('编辑'),
              ],
            ),
          ),
          PopupMenuItem(
            value: 'cards',
            child: Row(
              children: [
                Icon(
                  Icons.credit_card,
                  size: 16.sp,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                SizedBox(width: 8.w),
                const OxText('管理卡片'),
              ],
            ),
          ),
          PopupMenuItem(
            value: 'duplicate',
            child: Row(
              children: [
                Icon(
                  Icons.copy,
                  size: 16.sp,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                SizedBox(width: 8.w),
                const OxText('复制'),
              ],
            ),
          ),
          PopupMenuItem(
            value: 'delete',
            child: Row(
              children: [
                Icon(
                  Icons.delete,
                  size: 16.sp,
                  color: Theme.of(context).colorScheme.error,
                ),
                SizedBox(width: 8.w),
                OxText(
                  '删除',
                  color: Theme.of(context).colorScheme.error,
                ),
              ],
            ),
          ),
        ],
        onSelected: (value) {
          switch (value) {
            case 'edit':
              controller.editBook(book);
              break;
            case 'cards':
              controller.manageBookCards(book);
              break;
            case 'duplicate':
              controller.duplicateBook(book);
              break;
            case 'delete':
              controller.deleteBook(book);
              break;
          }
        },
      ),
    );
  }

  Widget _buildMetaInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Theme.of(context)
            .colorScheme
            .surfaceContainerHighest
            .withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.schedule,
            size: 12.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: 4.w),
          OxText(
            _formatTime(book.createdAt),
            fontSize: 11.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
        ],
      ),
    );
  }

  String _formatTime(String? timeStr) {
    if (timeStr == null) return '未知';
    try {
      final time = DateTime.parse(timeStr);
      final now = DateTime.now();
      final diff = now.difference(time);

      if (diff.inDays > 0) {
        return '${diff.inDays}天前';
      } else if (diff.inHours > 0) {
        return '${diff.inHours}小时前';
      } else if (diff.inMinutes > 0) {
        return '${diff.inMinutes}分钟前';
      } else {
        return '刚刚';
      }
    } catch (e) {
      return '未知';
    }
  }
}
