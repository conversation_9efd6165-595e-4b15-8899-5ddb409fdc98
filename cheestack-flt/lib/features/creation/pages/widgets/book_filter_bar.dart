import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/theme.dart';
import 'package:cheestack_flt/shared/widgets/index.dart';
import '../../controllers/creation_controller.dart';

/// 书籍筛选栏组件
class BookFilterBar extends StatelessWidget {
  final CreationController controller;
  final bool isVisible;

  const BookFilterBar({
    super.key,
    required this.controller,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Container(
      padding: AppTheme.paddingMedium,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQuickFilters(context),
          if (controller.isFilterActive) ...[
            _buildActiveFilters(context),
          ],
        ],
      ),
    );
  }

  /// 构建快速筛选标签
  Widget _buildQuickFilters(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip(
            context,
            label: '全部',
            isSelected: !controller.isFilterActive,
            onTap: () => controller.clearAllFilters(),
          ),
          SizedBox(width: 8.w),
          _buildFilterChip(
            context,
            label: '公开',
            isSelected: controller.selectedPrivacyFilter == 'free',
            onTap: () => controller.setPrivacyFilter('free'),
          ),
          SizedBox(width: 6.w),
          _buildFilterChip(
            context,
            label: '私有',
            isSelected: controller.selectedPrivacyFilter == 'private',
            onTap: () => controller.setPrivacyFilter('private'),
          ),
          SizedBox(width: 6.w),
          _buildFilterChip(
            context,
            label: '收费',
            isSelected: controller.selectedPrivacyFilter == 'paid',
            onTap: () => controller.setPrivacyFilter('paid'),
          ),
          SizedBox(width: 6.w),
          _buildFilterChip(
            context,
            label: '会员',
            isSelected: controller.selectedPrivacyFilter == 'member_free',
            onTap: () => controller.setPrivacyFilter('member_free'),
          ),
          SizedBox(width: 8.w),
          _buildFilterChip(
            context,
            label: '今天',
            isSelected: controller.selectedTimeFilter == 'today',
            onTap: () => controller.setTimeFilter('today'),
          ),
          SizedBox(width: 8.w),
          _buildFilterChip(
            context,
            label: '本周',
            isSelected: controller.selectedTimeFilter == 'week',
            onTap: () => controller.setTimeFilter('week'),
          ),
          SizedBox(width: 8.w),
          _buildFilterChip(
            context,
            label: '本月',
            isSelected: controller.selectedTimeFilter == 'month',
            onTap: () => controller.setTimeFilter('month'),
          ),
        ],
      ),
    );
  }

  /// 构建筛选标签
  Widget _buildFilterChip(
    BuildContext context, {
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
        ),
        child: OxText(
          label,
          fontSize: 11.sp,
          color: isSelected
              ? Theme.of(context).colorScheme.onPrimary
              : Theme.of(context).colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建当前激活的筛选条件
  Widget _buildActiveFilters(BuildContext context) {
    List<Widget> activeFilters = [];

    if (controller.selectedPrivacyFilter != 'all') {
      activeFilters.add(_buildActiveFilterChip(
        context,
        label:
            controller.getPrivacyFilterText(controller.selectedPrivacyFilter),
        onRemove: () => controller.setPrivacyFilter('all'),
      ));
    }

    if (controller.selectedTimeFilter != 'all') {
      activeFilters.add(_buildActiveFilterChip(
        context,
        label: controller.getTimeFilterText(controller.selectedTimeFilter),
        onRemove: () => controller.setTimeFilter('all'),
      ));
    }

    if (controller.bookSearchKeyword.isNotEmpty) {
      activeFilters.add(_buildActiveFilterChip(
        context,
        label: '搜索: ${controller.bookSearchKeyword}',
        onRemove: () => controller.onBookSearchChanged(''),
      ));
    }

    if (activeFilters.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            OxText(
              '当前筛选:',
              fontSize: AppTheme.fontSmall,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const Spacer(),
            GestureDetector(
              onTap: controller.clearAllFilters,
              child: OxText(
                '清除全部',
                fontSize: AppTheme.fontSmall,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 4.h,
          children: activeFilters,
        ),
      ],
    );
  }

  /// 构建激活的筛选条件标签
  Widget _buildActiveFilterChip(
    BuildContext context, {
    required String label,
    required VoidCallback onRemove,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          OxText(
            label,
            fontSize: AppTheme.fontSmall,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
          SizedBox(width: 4.w),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Icons.close,
              size: 14.sp,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }
}

/// 筛选面板组件 - 全新现代化设计
class BookFilterPanel extends StatelessWidget {
  final CreationController controller;

  const BookFilterPanel({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CreationController>(
      builder: (ctrl) {
        return Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context)
                    .colorScheme
                    .shadow
                    .withValues(alpha: 0.15),
                blurRadius: 20,
                offset: const Offset(0, -4),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部拖拽指示器
              _buildDragIndicator(context),

              // 头部
              _buildModernHeader(context),

              // 内容区域
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 8.h),
                      _buildModernPrivacySection(context, ctrl),
                      SizedBox(height: 24.h),
                      _buildModernTimeSection(context, ctrl),
                      SizedBox(height: 32.h),
                    ],
                  ),
                ),
              ),

              // 底部操作区
              _buildModernActions(context, ctrl),
            ],
          ),
        );
      },
    );
  }

  // 拖拽指示器
  Widget _buildDragIndicator(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 12.h, bottom: 8.h),
      width: 40.w,
      height: 4.h,
      decoration: BoxDecoration(
        color: Theme.of(context)
            .colorScheme
            .onSurfaceVariant
            .withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(2.r),
      ),
    );
  }

  // 现代化头部
  Widget _buildModernHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .colorScheme
                  .primaryContainer
                  .withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.tune,
                  size: 18.sp,
                  color: Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: 6.w),
                OxText(
                  '筛选',
                  fontSize: AppTheme.fontTitle,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () => Get.back(),
            child: Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                Icons.close,
                size: 20.sp,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 现代化隐私筛选部分
  Widget _buildModernPrivacySection(
      BuildContext context, CreationController ctrl) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .primaryContainer
                      .withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Icon(
                  Icons.security,
                  size: 20.sp,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              SizedBox(width: 12.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  OxText(
                    '隐私设置',
                    fontSize: AppTheme.fontBody,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  SizedBox(height: 2.h),
                  OxText(
                    '选择要查看的书籍类型',
                    fontSize: AppTheme.fontSmall,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 20.h),
          _buildModernFilterGrid(
            context,
            ctrl,
            ['all', 'free', 'private', 'paid', 'member_free'],
            ctrl.selectedPrivacyFilter,
            (filter) => ctrl.setPrivacyFilter(filter),
            (filter) => ctrl.getPrivacyFilterText(filter),
            _getPrivacyIcon,
          ),
        ],
      ),
    );
  }

  // 现代化时间筛选部分
  Widget _buildModernTimeSection(
      BuildContext context, CreationController ctrl) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .secondaryContainer
                      .withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Icon(
                  Icons.schedule,
                  size: 20.sp,
                  color: Theme.of(context).colorScheme.secondary,
                ),
              ),
              SizedBox(width: 12.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  OxText(
                    '创建时间',
                    fontSize: AppTheme.fontBody,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  SizedBox(height: 2.h),
                  OxText(
                    '按创建时间筛选书籍',
                    fontSize: AppTheme.fontSmall,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 20.h),
          _buildModernFilterGrid(
            context,
            ctrl,
            ['all', 'today', 'week', 'month'],
            ctrl.selectedTimeFilter,
            (filter) => ctrl.setTimeFilter(filter),
            (filter) => ctrl.getTimeFilterText(filter),
            _getTimeIcon,
          ),
        ],
      ),
    );
  }





  // 现代化筛选网格
  Widget _buildModernFilterGrid(
    BuildContext context,
    CreationController ctrl,
    List<String> options,
    String selectedValue,
    Function(String) onSelect,
    String Function(String) getLabel,
    IconData Function(String) getIcon,
  ) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 2.2,
        crossAxisSpacing: 8.w,
        mainAxisSpacing: 10.h,
      ),
      itemCount: options.length,
      itemBuilder: (context, index) {
        final option = options[index];
        final isSelected = selectedValue == option;

        return GestureDetector(
          onTap: () => onSelect(option),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 10.h),
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context)
                        .colorScheme
                        .outline
                        .withValues(alpha: 0.2),
                width: isSelected ? 2 : 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : [
                      BoxShadow(
                        color: Theme.of(context)
                            .colorScheme
                            .shadow
                            .withValues(alpha: 0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  getIcon(option),
                  size: 16.sp,
                  color: isSelected
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                SizedBox(width: 6.w),
                Flexible(
                  child: OxText(
                    getLabel(option),
                    fontSize: 12.sp,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? Theme.of(context).colorScheme.onPrimary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 现代化底部操作区
  Widget _buildModernActions(BuildContext context, CreationController ctrl) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: ctrl.clearAllFilters,
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(16.r),
                    border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .outline
                          .withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.refresh,
                        size: 20.sp,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      SizedBox(width: 8.w),
                      OxText(
                        '重置',
                        fontSize: AppTheme.fontBody,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              flex: 2,
              child: GestureDetector(
                onTap: () => Get.back(),
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary,
                        Theme.of(context)
                            .colorScheme
                            .primary
                            .withValues(alpha: 0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16.r),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.check,
                        size: 20.sp,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                      SizedBox(width: 8.w),
                      OxText(
                        '确定',
                        fontSize: AppTheme.fontBody,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 获取隐私类型图标
  IconData _getPrivacyIcon(String privacy) {
    switch (privacy) {
      case 'free':
        return Icons.public;
      case 'private':
        return Icons.lock;
      case 'paid':
        return Icons.monetization_on;
      case 'member_free':
        return Icons.star;
      default:
        return Icons.apps;
    }
  }

  // 获取时间类型图标
  IconData _getTimeIcon(String time) {
    switch (time) {
      case 'today':
        return Icons.today;
      case 'week':
        return Icons.date_range;
      case 'month':
        return Icons.calendar_month;
      default:
        return Icons.all_inclusive;
    }
  }
}
