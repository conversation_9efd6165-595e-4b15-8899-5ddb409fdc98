import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';
import 'package:cheestack_flt/features/creation/models/book_model.dart';

void main() {
  group('书籍筛选UI优化测试', () {
    late CreationController controller;

    setUp(() {
      Get.testMode = true;
      controller = CreationController();
    });

    tearDown(() {
      Get.reset();
    });

    test('筛选功能应该移除排序相关内容', () {
      // 验证筛选功能不再包含排序逻辑
      // 这里主要测试数据逻辑，UI测试需要widget测试
      
      final testBooks = [
        BookModel(id: 1, name: '公开书籍', privacy: 'free'),
        BookModel(id: 2, name: '私有书籍', privacy: 'private'),
      ];

      controller.bookList.assignAll(testBooks);

      // 筛选功能应该独立于排序功能
      controller.setPrivacyFilter('free');
      expect(controller.filteredBookList.length, equals(1));
      expect(controller.filteredBookList.first.privacy, equals('free'));

      // 排序功能应该独立存在
      controller.setSortOption('name_asc');
      expect(controller.selectedSortOption, equals('name_asc'));
    });

    test('网格视图宽高比调整应该正确', () {
      // 验证网格视图的配置
      // 这里测试的是逻辑，实际的宽高比在UI层面
      
      final testBooks = [
        BookModel(id: 1, name: '测试书籍1', privacy: 'free'),
        BookModel(id: 2, name: '测试书籍2', privacy: 'private'),
        BookModel(id: 3, name: '测试书籍3', privacy: 'paid'),
        BookModel(id: 4, name: '测试书籍4', privacy: 'member_free'),
      ];

      controller.bookList.assignAll(testBooks);
      controller.applyFiltersAndSort();

      // 验证所有书籍都能正确显示
      expect(controller.filteredBookList.length, equals(4));
      
      // 验证网格视图模式
      controller.isGridView = true;
      expect(controller.isGridView, isTrue);
    });

    test('隐私筛选应该支持所有类型', () {
      final testBooks = [
        BookModel(id: 1, name: '公开书籍', privacy: 'free'),
        BookModel(id: 2, name: '私有书籍', privacy: 'private'),
        BookModel(id: 3, name: '收费书籍', privacy: 'paid'),
        BookModel(id: 4, name: '会员书籍', privacy: 'member_free'),
      ];

      controller.bookList.assignAll(testBooks);

      // 测试每种隐私类型的筛选
      final privacyTypes = ['free', 'private', 'paid', 'member_free'];
      
      for (final privacyType in privacyTypes) {
        controller.setPrivacyFilter(privacyType);
        expect(controller.filteredBookList.length, equals(1));
        expect(controller.filteredBookList.first.privacy, equals(privacyType));
      }

      // 测试全部显示
      controller.setPrivacyFilter('all');
      expect(controller.filteredBookList.length, equals(4));
    });

    test('筛选状态管理应该正确', () {
      // 初始状态
      expect(controller.selectedPrivacyFilter, equals('all'));
      expect(controller.selectedTimeFilter, equals('all'));
      expect(controller.isFilterActive, isFalse);

      // 设置筛选
      controller.setPrivacyFilter('private');
      expect(controller.selectedPrivacyFilter, equals('private'));
      expect(controller.isFilterActive, isTrue);

      // 清除筛选
      controller.clearAllFilters();
      expect(controller.selectedPrivacyFilter, equals('all'));
      expect(controller.selectedTimeFilter, equals('all'));
      expect(controller.isFilterActive, isFalse);
    });

    test('筛选文本显示应该正确', () {
      // 测试所有隐私类型的显示文本
      expect(controller.getPrivacyFilterText('all'), equals('全部'));
      expect(controller.getPrivacyFilterText('free'), equals('公开'));
      expect(controller.getPrivacyFilterText('private'), equals('私有'));
      expect(controller.getPrivacyFilterText('paid'), equals('收费'));
      expect(controller.getPrivacyFilterText('member_free'), equals('会员'));
    });

    test('时间筛选应该正确工作', () {
      final now = DateTime.now();
      final testBooks = [
        BookModel(
          id: 1,
          name: '今天的书籍',
          privacy: 'free',
          createdAt: now.toIso8601String(),
        ),
        BookModel(
          id: 2,
          name: '昨天的书籍',
          privacy: 'free',
          createdAt: now.subtract(const Duration(days: 1)).toIso8601String(),
        ),
        BookModel(
          id: 3,
          name: '上周的书籍',
          privacy: 'free',
          createdAt: now.subtract(const Duration(days: 8)).toIso8601String(),
        ),
      ];

      controller.bookList.assignAll(testBooks);

      // 测试今天筛选
      controller.setTimeFilter('today');
      expect(controller.filteredBookList.length, equals(1));
      expect(controller.filteredBookList.first.name, equals('今天的书籍'));

      // 测试本周筛选
      controller.setTimeFilter('week');
      expect(controller.filteredBookList.length, equals(2));

      // 测试全部时间
      controller.setTimeFilter('all');
      expect(controller.filteredBookList.length, equals(3));
    });

    test('复合筛选应该正确工作', () {
      final now = DateTime.now();
      final testBooks = [
        BookModel(
          id: 1,
          name: '今天的公开书籍',
          privacy: 'free',
          createdAt: now.toIso8601String(),
        ),
        BookModel(
          id: 2,
          name: '今天的私有书籍',
          privacy: 'private',
          createdAt: now.toIso8601String(),
        ),
        BookModel(
          id: 3,
          name: '昨天的公开书籍',
          privacy: 'free',
          createdAt: now.subtract(const Duration(days: 1)).toIso8601String(),
        ),
      ];

      controller.bookList.assignAll(testBooks);

      // 复合筛选：今天的公开书籍
      controller.setPrivacyFilter('free');
      controller.setTimeFilter('today');
      expect(controller.filteredBookList.length, equals(1));
      expect(controller.filteredBookList.first.name, equals('今天的公开书籍'));

      // 复合筛选：今天的私有书籍
      controller.setPrivacyFilter('private');
      controller.setTimeFilter('today');
      expect(controller.filteredBookList.length, equals(1));
      expect(controller.filteredBookList.first.name, equals('今天的私有书籍'));

      // 复合筛选：本周的公开书籍
      controller.setPrivacyFilter('free');
      controller.setTimeFilter('week');
      expect(controller.filteredBookList.length, equals(2));
    });

    test('筛选后的实时更新应该正确', () {
      final testBooks = [
        BookModel(id: 1, name: '公开书籍1', privacy: 'free'),
        BookModel(id: 2, name: '公开书籍2', privacy: 'free'),
        BookModel(id: 3, name: '私有书籍', privacy: 'private'),
      ];

      controller.bookList.assignAll(testBooks);

      // 筛选公开书籍
      controller.setPrivacyFilter('free');
      expect(controller.filteredBookList.length, equals(2));

      // 删除一本公开书籍
      controller.bookList.removeWhere((book) => book.id == 1);
      controller.applyFiltersAndSort();
      expect(controller.filteredBookList.length, equals(1));
      expect(controller.filteredBookList.first.name, equals('公开书籍2'));

      // 添加新的公开书籍
      controller.bookList.add(BookModel(id: 4, name: '新公开书籍', privacy: 'free'));
      controller.applyFiltersAndSort();
      expect(controller.filteredBookList.length, equals(2));
      expect(controller.filteredBookList.any((book) => book.name == '新公开书籍'), isTrue);
    });
  });
}
